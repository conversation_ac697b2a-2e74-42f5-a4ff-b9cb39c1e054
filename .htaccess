# Protect sensitive files
<Files ".env">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
    <RequireAll>
        Require local
        Require expr %{HTTP_HOST} == "yourdomain.com"
    </RequireAll>
</Files>

# Protect backup files and logs
<FilesMatch "\.(bak|backup|log|sql)$">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Prevent access to PHP files in submissions folder
<Directory "submissions">
    <FilesMatch "\.php$">
        Order deny,allow
        Deny from all
    </FilesMatch>
</Directory>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Hide server information
ServerTokens Prod
ServerSignature Off

# Prevent access to directory listings
Options -Indexes